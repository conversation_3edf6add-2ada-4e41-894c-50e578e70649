"""
RAG检索系统
负责文档嵌入、相似度搜索和知识检索功能
"""
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any
from loguru import logger
import numpy as np
import sys
from pathlib import Path
import gc
import psutil
import os

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.milvus_manager import MilvusManager
from idconfig.config import Config
from rag.bge_m3_optimizer import BGEM3Optimizer

class RAGSystem:
    def __init__(self):
        self.config = Config()
        self.embedding_model = None
        self.milvus_manager = MilvusManager()
        self.bge_optimizer = None  # BGE-M3 优化器
        
    def initialize(self):
        """初始化RAG系统"""
        try:
            # 初始化嵌入模型
            logger.info(f"加载嵌入模型: {self.config.EMBEDDING_MODEL}")
            self.embedding_model = SentenceTransformer(self.config.EMBEDDING_MODEL)
            logger.info("嵌入模型加载完成")

            # 验证模型配置
            if not self._validate_model_config():
                return False

            # 初始化Milvus管理器
            if not self.milvus_manager.initialize():
                logger.error("Milvus管理器初始化失败")
                return False

            logger.info("RAG系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"RAG系统初始化失败: {e}")
            return False

    def _validate_model_config(self):
        """验证模型配置"""
        try:
            # 测试编码一个简单文本
            test_text = "测试文本"
            test_embedding = self.embedding_model.encode(test_text, normalize_embeddings=True)

            actual_dim = len(test_embedding)
            expected_dim = self.config.VECTOR_DIM

            logger.info(f"模型实际维度: {actual_dim}, 配置维度: {expected_dim}")

            # 检查是否为BGE-M3模型
            if "bge-m3" in self.config.EMBEDDING_MODEL.lower():
                if actual_dim != 1024:
                    logger.error(f"BGE-M3模型应该是1024维，但检测到{actual_dim}维")
                    return False
                if expected_dim != 1024:
                    logger.error("BGE-M3模型需要设置VECTOR_DIM=1024")
                    return False
                logger.info("✓ BGE-M3模型配置验证通过")

            # 检查向量是否归一化
            norm = np.linalg.norm(test_embedding)
            if abs(norm - 1.0) > 0.01:
                logger.warning(f"向量未完全归一化，norm={norm:.4f}")
            else:
                logger.info("✓ 向量归一化验证通过")

            return True

        except Exception as e:
            logger.error(f"模型配置验证失败: {e}")
            return False
    
    def encode_text(self, text: str) -> List[float]:
        """将文本编码为向量"""
        import time
        import platform
        from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

        def encode_with_timeout():
            """在单独线程中执行编码，支持超时"""
            return self.embedding_model.encode(text, normalize_embeddings=True)

        try:
            if not self.embedding_model:
                logger.error("嵌入模型未初始化")
                return []

            start_time = time.time()

            # 使用线程池实现跨平台超时机制
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(encode_with_timeout)
                try:
                    # 设置30秒超时
                    embedding = future.result(timeout=30)
                except FutureTimeoutError:
                    raise TimeoutError("向量编码超时")

            encode_time = time.time() - start_time
            if encode_time > 5:  # 如果编码时间超过5秒，记录警告
                logger.warning(f"向量编码耗时较长: {encode_time:.2f}秒，文本长度: {len(text)}")

            # 检查向量维度
            actual_dim = len(embedding)
            expected_dim = self.config.VECTOR_DIM

            if actual_dim != expected_dim:
                logger.warning(f"向量维度不匹配: {actual_dim} != {expected_dim}")
                # BGE-M3应该是1024维，如果不匹配说明配置有问题
                if actual_dim == 1024 and expected_dim != 1024:
                    logger.error("检测到BGE-M3模型(1024维)，但配置的VECTOR_DIM不是1024")
                    logger.error("请在config.py中设置 VECTOR_DIM = 1024")
                    return []

                # 如果维度不匹配，进行填充或截断（不推荐）
                if actual_dim < expected_dim:
                    embedding = np.pad(embedding, (0, expected_dim - actual_dim))
                    logger.warning(f"向量已填充到{expected_dim}维")
                else:
                    embedding = embedding[:expected_dim]
                    logger.warning(f"向量已截断到{expected_dim}维")

            # 确保向量是归一化的（BGE-M3推荐使用归一化向量）
            if not hasattr(embedding, 'dtype'):
                embedding = np.array(embedding, dtype=np.float32)

            # 检查向量是否有效
            if np.any(np.isnan(embedding)) or np.any(np.isinf(embedding)):
                logger.error("生成的向量包含无效值(NaN或Inf)")
                return []

            return embedding.tolist()

        except TimeoutError:
            logger.error(f"向量编码超时，文本长度: {len(text)}")
            return []
        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            return []
    
    def add_knowledge(self, content: str, category: str, source: str = ""):
        """添加知识到知识库"""
        try:
            # 生成嵌入向量
            embedding = self.encode_text(content)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "content": content,
                "category": category,
                "source": source,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_knowledge(data)
            
        except Exception as e:
            logger.error(f"添加知识失败: {e}")
            return False
    
    def add_conversation_history(self, session_id: str, user_query: str, 
                               assistant_response: str, timestamp: int):
        """添加对话历史"""
        try:
            # 将用户查询和助手回复组合作为嵌入内容
            combined_text = f"用户: {user_query}\n助手: {assistant_response}"
            
            # 生成嵌入向量
            embedding = self.encode_text(combined_text)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "session_id": session_id,
                "user_query": user_query,
                "assistant_response": assistant_response,
                "timestamp": timestamp,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_history(data)
            
        except Exception as e:
            logger.error(f"添加对话历史失败: {e}")
            return False
    
    def search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索知识库
            results = self.milvus_manager.search_knowledge(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"知识库搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索知识失败: {e}")
            return []
    
    def search_conversation_history(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关对话历史"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索历史对话
            results = self.milvus_manager.search_history(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"历史对话搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索对话历史失败: {e}")
            return []
    
    def retrieve_context(self, query: str, include_images: bool = True) -> Dict[str, Any]:
        """检索相关上下文信息"""
        try:
            # 搜索知识库
            knowledge_results = self.search_knowledge(query)

            # 搜索历史对话
            history_results = self.search_conversation_history(query)

            # 搜索相关图片（如果启用）
            image_results = []
            if include_images:
                image_results = self.search_related_images(query)

            # 组织上下文信息
            context = {
                "knowledge": knowledge_results,
                "history": history_results,
                "images": image_results,
                "query": query
            }

            logger.info(f"上下文检索完成 - 知识: {len(knowledge_results)}条, 历史: {len(history_results)}条, 图片: {len(image_results)}张")
            return context

        except Exception as e:
            logger.error(f"检索上下文失败: {e}")
            return {"knowledge": [], "history": [], "images": [], "query": query}
    
    def batch_add_knowledge(self, knowledge_list: List[Dict[str, str]]):
        """批量添加知识"""
        try:
            logger.info(f"开始批量添加 {len(knowledge_list)} 个知识块到向量数据库")
            data = []
            total_count = len(knowledge_list)

            # 分批处理，避免内存问题
            batch_size = 50  # 每批处理50个
            for i, item in enumerate(knowledge_list):
                try:
                    # 记录进度
                    if (i + 1) % 10 == 0 or i == 0:
                        logger.info(f"正在生成向量: {i + 1}/{total_count} ({((i + 1) / total_count * 100):.1f}%)")

                    embedding = self.encode_text(item["content"])
                    if embedding:
                        data.append({
                            "content": item["content"],
                            "category": item.get("category", "general"),
                            "source": item.get("source", ""),
                            "embedding": embedding
                        })
                    else:
                        logger.warning(f"跳过无法生成向量的知识块: {item.get('content', '')[:100]}...")

                    # 分批插入到数据库
                    if len(data) >= batch_size:
                        # 检查内存使用情况
                        memory_percent = psutil.virtual_memory().percent
                        if memory_percent > 80:
                            logger.warning(f"内存使用率较高: {memory_percent:.1f}%，执行垃圾回收")
                            gc.collect()

                        logger.info(f"插入批次数据到数据库: {len(data)} 个知识块 (内存使用: {memory_percent:.1f}%)")
                        if not self.milvus_manager.insert_knowledge(data):
                            logger.error(f"批次插入失败，已处理 {i + 1} 个知识块")
                            return False
                        data = []  # 清空已处理的数据
                        gc.collect()  # 强制垃圾回收

                except Exception as e:
                    logger.error(f"处理第 {i + 1} 个知识块时出错: {e}")
                    continue

            # 插入剩余的数据
            if data:
                logger.info(f"插入最后批次数据到数据库: {len(data)} 个知识块")
                if not self.milvus_manager.insert_knowledge(data):
                    logger.error("最后批次插入失败")
                    return False

            logger.info(f"成功完成批量添加，共处理 {total_count} 个知识块")
            return True

        except Exception as e:
            logger.error(f"批量添加知识失败: {e}")
            return False

    def search_related_images(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索与查询相关的图片"""
        try:
            # 导入多模态检索器
            from rag.multimodal_retrieval import MultimodalImageRetriever

            # 创建多模态检索器实例
            multimodal_retriever = MultimodalImageRetriever()
            if not multimodal_retriever.initialize():
                logger.warning("多模态检索器初始化失败，跳过图片搜索")
                return []

            # 搜索相关图片
            image_results = multimodal_retriever.search_images_by_text(query, top_k)

            # 过滤和格式化结果
            formatted_results = []
            for result in image_results:
                # 使用final_score作为主要评分标准
                score = result.get("final_score", result.get("similarity", 0))
                if score >= 0.2:  # 设置相似度阈值
                    formatted_result = {
                        "image_id": result.get("image_id"),
                        "pdf_name": result.get("pdf_name"),
                        "page_number": result.get("page_number"),
                        "image_index": result.get("image_index"),
                        "description": result.get("description"),
                        "image_type": result.get("image_type"),
                        "similarity": result.get("similarity", 0),
                        "final_score": score,
                        "width": result.get("width"),
                        "height": result.get("height")
                    }
                    formatted_results.append(formatted_result)

            logger.info(f"图片搜索完成，找到 {len(formatted_results)} 张相关图片")
            return formatted_results

        except ImportError:
            logger.warning("多模态功能不可用，跳过图片搜索")
            return []
        except Exception as e:
            logger.error(f"搜索相关图片失败: {e}")
            return []
